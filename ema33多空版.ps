//@version=6

//声明==========================================================================
strategy(title = 'EMA33多空动态止损版', overlay = true, precision = 2, pyramiding = 1, initial_capital = 10000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, slippage = 0, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

//设置==========================================================================
// 基础参数
ma1_input = input.int(33, 'MA1', step = 1)
ma2_input = input.int(33, 'MA2', step = 1)
oppercent_1 = input.float(1.0, '突破MA1偏离度[%]', step = 0.1)
oppercent_2 = input.float(0, '跌破MA2偏离度[%]', step = 0.1)
slpercent_1 = input.float(2.0, '止损[%]', step = 0.1)

// ======== 📈 交易方向设置 ========
group_direction = "📈 交易方向设置"
enable_long = input.bool(true, '启用做多', group = group_direction)
enable_short = input.bool(false, '启用做空', group = group_direction)

// ======== 🎯 止盈设置 ========
group_tp = "🎯 止盈设置"
tp_mode = input.string("信号止盈", "止盈模式", options=["信号止盈", "固定止盈", "分阶段止盈"], group = group_tp)
fixed_tp_percent = input.float(3.0, '固定止盈[%]', step = 0.1, minval = 0.5, maxval = 2000.0, group = group_tp)

// 分阶段止盈参数
stage_tp_1_percent = input.float(1.5, '第一阶段止盈[%]', step = 0.1, minval = 0.5, maxval = 100.0, group = group_tp)
stage_tp_1_qty = input.float(50.0, '第一阶段平仓比例[%]', step = 5.0, minval = 10.0, maxval = 90.0, group = group_tp)
stage_tp_2_percent = input.float(3.0, '第二阶段止盈[%]', step = 0.1, minval = 1.0, maxval = 2000.0, group = group_tp)
stage_tp_2_qty = input.float(100.0, '第二阶段平仓比例[%]', step = 5.0, minval = 10.0, maxval = 100.0, group = group_tp)

// ======== ⏰ 时间风控系统 ========
group_time_control = "⏰ 时间风控系统"
enable_golden_time = input.bool(true, '启用黄金时段限制', group = group_time_control)
golden_start_hour = input.int(9, '黄金时段开始时间', minval = 0, maxval = 23, group = group_time_control)
golden_end_hour = input.int(11, '黄金时段结束时间', minval = 0, maxval = 23, group = group_time_control)
enable_us_session = input.bool(true, '启用美股时段', group = group_time_control)
enable_death_time = input.bool(true, '启用死亡时间限制', group = group_time_control)
avoid_friday_night = input.bool(true, '避开周五晚间', group = group_time_control)

// 交易时段设置
group_trading_time = '📅 交易时段'
tradingtime = input.bool(true, '启用交易时段', group = group_trading_time)
asia_session = input.bool(true, '亚洲时段 09:00-13:00', group = group_trading_time)
london_session = input.bool(true, '伦敦时段 15:00-18:00', group = group_trading_time)
us_am_session = input.bool(true, '美国早盘 21:30-00:00', group = group_trading_time)
us_pm_session = input.bool(true, '美国下午 02:30-05:00', group = group_trading_time)
custom_session = input.bool(false, '启用自定义时段', group = group_trading_time)

// 自定义时段参数
custom_start_h = input.int(20, '开始小时', group = group_trading_time, minval=0, maxval=23, tooltip='仅在启用自定义时段时有效')
custom_start_m = input.int(0, '开始分钟', group = group_trading_time, minval=0, maxval=59)
custom_end_h = input.int(23, '结束小时', group = group_trading_time, minval=0, maxval=24)
custom_end_m = input.int(59, '结束分钟', group = group_trading_time, minval=0, maxval=59)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 🔄 动态止损系统 ========
group_dynamic_sl = "🔄 动态止损系统"
enable_dynamic_sl = input.bool(false, '启用动态止损', group = group_dynamic_sl)
trailing_start_percent = input.float(1.0, '启动跟踪止损盈利[%]', step = 0.1, minval = 0.5, maxval = 500.0, group = group_dynamic_sl)
trailing_step_percent = input.float(0.5, '跟踪止损步长[%]', step = 0.1, minval = 0.1, maxval = 100.0, group = group_dynamic_sl)
breakeven_percent = input.float(1.5, '保本止损触发[%]', step = 0.1, minval = 0.5, maxval = 30.0, group = group_dynamic_sl)

//定义==========================================================================
// ======== 时间判断函数 ========
in_session(s_hour, s_minute, e_hour, e_minute) =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_h = hour(exchange_time)
    exchange_m = minute(exchange_time)

    // 处理跨日情况
    start_day_offset = s_hour < e_hour ? 0 : 1
    end_day_offset = s_hour < e_hour ? 0 : 1
    (exchange_h > s_hour or (exchange_h == s_hour and exchange_m >= s_minute)) and (exchange_h < e_hour or (exchange_h == e_hour and exchange_m <= e_minute))

// 夏令时判断函数
is_dst() =>
    year_var = year(time)
    // 3月的第二个星期日（夏令时开始）
    march_date = timestamp(year_var, 3, 8)
    march_day = dayofweek(march_date) == dayofweek.sunday ? march_date : march_date + (7 - dayofweek(march_date) + 1) * 86400000
    dst_start = march_day + (7 * 86400000)

    // 11月的第一个星期日（夏令时结束）
    november_date = timestamp(year_var, 11, 1)
    november_day = dayofweek(november_date) == dayofweek.sunday ? november_date : november_date + (7 - dayofweek(november_date) + 1) * 86400000
    dst_end = november_day

    time >= dst_start and time < dst_end

// 美国时段判断逻辑
get_us_session() =>
    var int us_am_start_h = is_dst() ? 21 : 22  // 北京时间
    var int us_am_start_m = is_dst() ? 30 : 30
    var int us_am_end_h = is_dst() ? 4 : 5      // 次日凌晨
    var int us_pm_start_h = is_dst() ? 2 : 3    // 美国下午时段对应北京时间
    var int us_pm_start_m = 30
    var int us_pm_end_h = is_dst() ? 5 : 6

    // 美国早盘时段（包含跨日判断）
    us_am = us_am_session and (in_session(us_am_start_h, us_am_start_m, 24, 0) or in_session(0, 0, us_am_end_h, 0))

    // 美国下午时段（次日凌晨）
    us_pm = us_pm_session and in_session(us_pm_start_h, us_pm_start_m, us_pm_end_h, 0)

    us_am or us_pm

// 交易时段判断
in_trading_time() =>
    if not tradingtime
        true
    else
        asia = asia_session and in_session(9, 0, 13, 0)
        london = london_session and in_session(15, 0, 18, 0)
        us = get_us_session()
        custom = custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)

        asia or london or us or custom

// EMA通道定义
ma1 = ta.ema(high, ma1_input)  // 上轨
ma2 = ta.ema(low, ma2_input)   // 下轨
// ma1   = ta.sma(close, ma1_input)
// ma2   = ta.sma(close,  ma2_input)

// 做多突破线（上轨+偏离度）
ma1op = ma1 * (1 + oppercent_1 / 100)
// 做空突破线（下轨-偏离度）
ma2op = ma2 * (1 - oppercent_2 / 100)

// 持仓信息
lastEntryPrice = strategy.opentrades.entry_price(strategy.opentrades - 1)
position_size = strategy.position_size
avg_price = strategy.position_avg_price

// ======== 动态止损系统 ========
// 动态止损变量
var float highest_profit_long = 0.0
var float lowest_profit_short = 0.0
var float dynamic_sl_long = 0.0
var float dynamic_sl_short = 0.0
var bool trailing_active_long = false
var bool trailing_active_short = false

// 当前盈利计算
current_profit_percent_long = position_size > 0 ? (close - avg_price) / avg_price * 100 : 0.0
current_profit_percent_short = position_size < 0 ? (avg_price - close) / avg_price * 100 : 0.0

// 重置动态止损状态
if position_size == 0
    highest_profit_long := 0.0
    lowest_profit_short := 0.0
    dynamic_sl_long := 0.0
    dynamic_sl_short := 0.0
    trailing_active_long := false
    trailing_active_short := false

// 做多动态止损逻辑
if position_size > 0 and enable_dynamic_sl
    // 更新最高盈利
    if current_profit_percent_long > highest_profit_long
        highest_profit_long := current_profit_percent_long

    // 启动跟踪止损
    if current_profit_percent_long >= trailing_start_percent and not trailing_active_long
        trailing_active_long := true
        dynamic_sl_long := close * (1 - trailing_step_percent / 100)

    // 更新跟踪止损
    if trailing_active_long
        new_sl = close * (1 - trailing_step_percent / 100)
        if new_sl > dynamic_sl_long
            dynamic_sl_long := new_sl

    // 保本止损
    if current_profit_percent_long >= breakeven_percent
        breakeven_sl = avg_price * (1 + 0.1 / 100)  // 保本+0.1%
        if dynamic_sl_long < breakeven_sl
            dynamic_sl_long := breakeven_sl

// 做空动态止损逻辑
if position_size < 0 and enable_dynamic_sl
    // 更新最高盈利（做空）
    if current_profit_percent_short > lowest_profit_short
        lowest_profit_short := current_profit_percent_short

    // 启动跟踪止损
    if current_profit_percent_short >= trailing_start_percent and not trailing_active_short
        trailing_active_short := true
        dynamic_sl_short := close * (1 + trailing_step_percent / 100)

    // 更新跟踪止损
    if trailing_active_short
        new_sl = close * (1 + trailing_step_percent / 100)
        if new_sl < dynamic_sl_short
            dynamic_sl_short := new_sl

    // 保本止损
    if current_profit_percent_short >= breakeven_percent
        breakeven_sl = avg_price * (1 - 0.1 / 100)  // 保本+0.1%
        if dynamic_sl_short > breakeven_sl
            dynamic_sl_short := breakeven_sl

// 最终止损价格（静态或动态）
final_sl_long = enable_dynamic_sl and trailing_active_long ? dynamic_sl_long : lastEntryPrice * (1 - slpercent_1 / 100)
final_sl_short = enable_dynamic_sl and trailing_active_short ? dynamic_sl_short : lastEntryPrice * (1 + slpercent_1 / 100)

// 止盈价格计算
// 固定止盈
fixed_tp_long = avg_price * (1 + fixed_tp_percent / 100)
fixed_tp_short = avg_price * (1 - fixed_tp_percent / 100)

// 分阶段止盈
stage_tp_1_long = avg_price * (1 + stage_tp_1_percent / 100)
stage_tp_1_short = avg_price * (1 - stage_tp_1_percent / 100)
stage_tp_2_long = avg_price * (1 + stage_tp_2_percent / 100)
stage_tp_2_short = avg_price * (1 - stage_tp_2_percent / 100)

// 分阶段止盈状态跟踪
var bool stage1_executed = false
var bool stage2_executed = false

// 重置分阶段状态
if position_size == 0
    stage1_executed := false
    stage2_executed := false

//条件==========================================================================
// ======== 时间风控条件 ========
// 时间风控 - 检查是否在交易禁忌时间
current_hour = hour(time, "UTC+8")
current_day = dayofweek(time)
is_golden_time = enable_golden_time ? (current_hour >= golden_start_hour and current_hour <= golden_end_hour) : true
is_death_time = enable_death_time and avoid_friday_night and current_day == dayofweek.friday and current_hour >= 18

// 综合时间判断
is_valid_trading_time = in_trading_time() and is_golden_time and not is_death_time

// 做多信号
long = close > ma1op and enable_long and is_valid_trading_time
long_tp = close < ma2

// 做空信号
short = close < ma2op and enable_short and is_valid_trading_time
short_tp = close > ma1

// 止盈条件判断
// 固定止盈条件
fixed_tp_long_condition = position_size > 0 and close >= fixed_tp_long
fixed_tp_short_condition = position_size < 0 and close <= fixed_tp_short

// 分阶段止盈条件
stage_tp_1_long_condition = position_size > 0 and close >= stage_tp_1_long and not stage1_executed
stage_tp_1_short_condition = position_size < 0 and close <= stage_tp_1_short and not stage1_executed
stage_tp_2_long_condition = position_size > 0 and close >= stage_tp_2_long and not stage2_executed
stage_tp_2_short_condition = position_size < 0 and close <= stage_tp_2_short and not stage2_executed

// 综合止盈条件
should_tp_long = tp_mode == "信号止盈" ? long_tp :
                 tp_mode == "固定止盈" ? fixed_tp_long_condition : false

should_tp_short = tp_mode == "信号止盈" ? short_tp :
                  tp_mode == "固定止盈" ? fixed_tp_short_condition : false

//显示==========================================================================
// EMA通道显示
stLongplot=plot(ma1, color = color.new(color.white, 0), title = 'EMA上轨')
stShortplot=plot(ma2, color = color.new(color.red, 0), title = 'EMA下轨')
fill(stLongplot,stShortplot,color= color.rgb(0,255,0,0.1))

// 突破线显示
plot(ma1op, color = color.new(color.green, 50), style = plot.style_line, linewidth = 1, title = '做多突破线')
plot(ma2op, color = color.new(color.red, 50), style = plot.style_line, linewidth = 1, title = '做空突破线')

// 持仓信息显示
plot(lastEntryPrice, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '开仓价')

// 止损线显示（静态和动态）
plot(position_size > 0 ? final_sl_long : position_size < 0 ? final_sl_short : na,
     color = enable_dynamic_sl ? color.new(color.purple, 60) : color.new(color.red, 80),
     style = plot.style_linebr, linewidth = 2, title = '止损线')

// 动态止损激活标记
plotshape(enable_dynamic_sl and position_size > 0 and trailing_active_long,
          style=shape.circle, location=location.absolute, color=color.purple, size=size.tiny, title="做多跟踪激活")
plotshape(enable_dynamic_sl and position_size < 0 and trailing_active_short,
          style=shape.circle, location=location.absolute, color=color.purple, size=size.tiny, title="做空跟踪激活")

// 止盈线显示
tp_color_long = color.new(color.blue, 60)
tp_color_short = color.new(color.orange, 60)

// 固定止盈线
plot(tp_mode == "固定止盈" and position_size > 0 ? fixed_tp_long : na,
     color = tp_color_long, style = plot.style_linebr, linewidth = 1, title = '做多止盈线')
plot(tp_mode == "固定止盈" and position_size < 0 ? fixed_tp_short : na,
     color = tp_color_short, style = plot.style_linebr, linewidth = 1, title = '做空止盈线')

// 分阶段止盈线
plot(tp_mode == "分阶段止盈" and position_size > 0 and not stage1_executed ? stage_tp_1_long : na,
     color = color.new(color.blue, 40), style = plot.style_linebr, linewidth = 1, title = '第一阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size > 0 and not stage2_executed ? stage_tp_2_long : na,
     color = color.new(color.blue, 60), style = plot.style_linebr, linewidth = 1, title = '第二阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size < 0 and not stage1_executed ? stage_tp_1_short : na,
     color = color.new(color.orange, 40), style = plot.style_linebr, linewidth = 1, title = '第一阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size < 0 and not stage2_executed ? stage_tp_2_short : na,
     color = color.new(color.orange, 60), style = plot.style_linebr, linewidth = 1, title = '第二阶段止盈')

// 信号标记
plotshape(long, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small, title="做多信号")
plotshape(short, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small, title="做空信号")

// 时间风控状态标记
plotshape(not is_valid_trading_time and (close > ma1op or close < ma2op),
          style=shape.xcross, location=location.abovebar, color=color.orange, size=size.small, title="禁止交易时段")
plotshape(is_death_time, style=shape.diamond, location=location.abovebar, color=color.purple, size=size.normal, title="死亡时间")

// 时间和动态止损信息面板
var table info_table = table.new(position.top_right, 3, 6, bgcolor=color.new(color.white, 80), border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "⏰ 交易状态", text_color=color.blue, text_size=size.normal)
    table.cell(info_table, 1, 0, "", text_color=color.black)
    table.cell(info_table, 2, 0, "", text_color=color.black)

    table.cell(info_table, 0, 1, "时段状态", text_color=color.black)
    trading_session_text = ""
    if asia_session and in_session(9, 0, 13, 0)
        trading_session_text := "亚洲"
    else if london_session and in_session(15, 0, 18, 0)
        trading_session_text := "伦敦"
    else if get_us_session()
        trading_session_text := "美股"
    else if custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)
        trading_session_text := "自定义"
    else
        trading_session_text := "休市"
    table.cell(info_table, 1, 1, trading_session_text, text_color=trading_session_text == "休市" ? color.gray : color.green)
    table.cell(info_table, 2, 1, is_golden_time ? "黄金时段" : "普通时段", text_color=is_golden_time ? color.green : color.gray)

    table.cell(info_table, 0, 2, "可交易", text_color=color.black)
    table.cell(info_table, 1, 2, is_valid_trading_time ? "✅允许" : "❌禁止", text_color=is_valid_trading_time ? color.green : color.red)
    table.cell(info_table, 2, 2, is_death_time ? "🚨死亡时间" : "正常", text_color=is_death_time ? color.red : color.green)

    table.cell(info_table, 0, 3, "动态止损", text_color=color.black)
    table.cell(info_table, 1, 3, enable_dynamic_sl ? "✅启用" : "❌关闭", text_color=enable_dynamic_sl ? color.green : color.gray)
    dynamic_status = ""
    if position_size > 0 and enable_dynamic_sl
        dynamic_status := trailing_active_long ? "🟢跟踪中" : "待激活"
    else if position_size < 0 and enable_dynamic_sl
        dynamic_status := trailing_active_short ? "🟢跟踪中" : "待激活"
    else
        dynamic_status := "无持仓"
    table.cell(info_table, 2, 3, dynamic_status, text_color=str.contains(dynamic_status, "跟踪") ? color.green : color.gray)

    table.cell(info_table, 0, 4, "当前盈利", text_color=color.black)
    current_profit_display = position_size > 0 ? str.tostring(current_profit_percent_long, "#.##") + "%" : position_size < 0 ? str.tostring(current_profit_percent_short, "#.##") + "%" : "0%"
    profit_color = position_size > 0 ? (current_profit_percent_long > 0 ? color.green : color.red) :
                   position_size < 0 ? (current_profit_percent_short > 0 ? color.green : color.red) : color.gray
    table.cell(info_table, 1, 4, current_profit_display, text_color=profit_color)
    table.cell(info_table, 2, 4, position_size > 0 ? "做多" : position_size < 0 ? "做空" : "无仓",
               text_color=position_size > 0 ? color.green : position_size < 0 ? color.red : color.gray)

    table.cell(info_table, 0, 5, "最高盈利", text_color=color.black)
    max_profit_display = position_size > 0 ? str.tostring(highest_profit_long, "#.##") + "%" : position_size < 0 ? str.tostring(lowest_profit_short, "#.##") + "%" : "0%"
    table.cell(info_table, 1, 5, max_profit_display, text_color=color.blue)
    table.cell(info_table, 2, 5, enable_dynamic_sl ? "动态保护" : "固定止损", text_color=enable_dynamic_sl ? color.purple : color.red)

//回测==========================================================================
ACT_BT = input.bool(true, title = '回测开关', group = '回测')
testStartYear = input.int(2023, title = '开始年份', minval = 1990, maxval = 2222, group = '回测')
testStartMonth = input.int(01, title = '开始月份', minval = 1, maxval = 12, group = '回测')
testStartDay = input.int(01, title = '开始日期', minval = 1, maxval = 31, group = '回测')
testPeriodStart = timestamp(testStartYear, testStartMonth, testStartDay, 0, 0)
testStopYear = input.int(2030, title = '终止年份', minval = 1980, maxval = 2222, group = '回测')
testStopMonth = input.int(12, title = '终止月份', minval = 1, maxval = 12, group = '回测')
testStopDay = input.int(31, title = '终止日期', minval = 1, maxval = 31, group = '回测')
testPeriodStop = timestamp(testStopYear, testStopMonth, testStopDay, 0, 0)
testPeriod = time >= testPeriodStart and time <= testPeriodStop ? true : false

//警报==========================================================================
alert_open_long = '做多开仓 bot '
alert_open_short = '做空开仓 bot '
alert_close = '平仓 bot '
alert_tp_stage1 = '第一阶段止盈 bot '
alert_tp_stage2 = '第二阶段止盈 bot '

//策略==========================================================================

if ACT_BT and testPeriod

    // ======== 开仓逻辑 ========
    // 做多开仓
    if long and strategy.position_size == 0
        strategy.entry('开多', strategy.long, comment = '📈做多', alert_message = alert_open_long)

    // 做空开仓
    if short and strategy.position_size == 0
        strategy.entry('开空', strategy.short, comment = '📉做空', alert_message = alert_open_short)

    // ======== 止损逻辑 ========
    // 做多止损（动态或固定）
    strategy.exit('多止损', '开多', stop = final_sl_long,
                  comment = enable_dynamic_sl ? '❌动态止损' : '❌固定止损', alert_message = alert_close)

    // 做空止损（动态或固定）
    strategy.exit('空止损', '开空', stop = final_sl_short,
                  comment = enable_dynamic_sl ? '❌动态止损' : '❌固定止损', alert_message = alert_close)

    // ======== 止盈逻辑 ========
    // 信号止盈和固定止盈
    if should_tp_long
        strategy.close('开多', comment = tp_mode == "信号止盈" ? '✅信号止盈' : '✅固定止盈', alert_message = alert_close)

    if should_tp_short
        strategy.close('开空', comment = tp_mode == "信号止盈" ? '✅信号止盈' : '✅固定止盈', alert_message = alert_close)

    // 分阶段止盈逻辑
    if tp_mode == "分阶段止盈"
        // 做多分阶段止盈
        if stage_tp_1_long_condition and not stage1_executed
            strategy.close('开多', qty_percent = stage_tp_1_qty, comment = '✅第一阶段止盈', alert_message = alert_tp_stage1)
            stage1_executed := true

        if stage_tp_2_long_condition and not stage2_executed
            strategy.close('开多', qty_percent = stage_tp_2_qty, comment = '✅第二阶段止盈', alert_message = alert_tp_stage2)
            stage2_executed := true

        // 做空分阶段止盈
        if stage_tp_1_short_condition and not stage1_executed
            strategy.close('开空', qty_percent = stage_tp_1_qty, comment = '✅第一阶段止盈', alert_message = alert_tp_stage1)
            stage1_executed := true

        if stage_tp_2_short_condition and not stage2_executed
            strategy.close('开空', qty_percent = stage_tp_2_qty, comment = '✅第二阶段止盈', alert_message = alert_tp_stage2)
            stage2_executed := true
