//@version=6
strategy(title="稳定综合滚仓策略", overlay=true, pyramiding=100, default_qty_type=strategy.percent_of_equity, currency=currency.USD, initial_capital=10000)

//=== 用户输入参数 ===
// 基础参数
strategyType = input.string("雨后的夏天", "策略类型", options=["雨后的夏天", "午饭浮云", "李法师Tony", "肥宅", "午饭维持", "Fil浮盈"])
tradeDirection = input.string("多空双向", "交易方向", options=["仅多", "仅空", "多空双向"])
confirmBars = input.int(1, "信号确认K线数", minval=1, maxval=4)
exchangeLeverage = input.float(100, "交易所杠杆(说明: 仅展示, 不直接放大下单比例)", minval=1)
capitalPercent = input.float(2, "基础资金百分比(%)", minval=0.1, maxval=100) / 100
maxPositionPercent = input.float(100, "最大总仓位上限(%)", minval=1, maxval=1000)
stopLossPerc = input.float(2, "基础止损比例(%)", minval=0.1, maxval=100) / 100

// 回测时间
enableBacktest = input.bool(true, "启用回测时间限制")
startYear = input.int(2022, "开始年份", minval=2000, maxval=2100)
startMonth = input.int(1, "开始月份", minval=1, maxval=12)
startDay = input.int(1, "开始日期", minval=1, maxval=31)
endYear = input.int(2025, "结束年份", minval=2000, maxval=2100)
endMonth = input.int(12, "结束月份", minval=1, maxval=12)
endDay = input.int(31, "结束日期", minval=1, maxval=31)

// 时间风控
enableSession = input.bool(true, "启用交易时段过滤")
enableAsia = input.bool(true, "亚洲: UTC+8 09:00-17:00")
enableLondon = input.bool(true, "伦敦: UTC 08:00-16:00")
enableUS = input.bool(true, "美国: UTC 14:30-21:00(简化14-21)")
enableWeekendFilter = input.bool(true, "过滤周末")
enableFridayNight = input.bool(true, "过滤周五18:00后(UTC)")

// 极端波动过滤
atrLen = input.int(14, "ATR长度")
atrPctTh = input.float(5.0, "ATR比例阈值(%)", step=0.1)
extremeVolMult = input.float(2.0, "极端波动系数(×ATR阈值)", step=0.1)

//=== 全局变量声明 ===
var float dynamicLeverage = 50.0
var int leverageStep = 0
var float compoundEquity = strategy.initial_capital
var int entryCount = 0
var float stopPrice = na
var bool positionActive = false

// 时间/波动派生量
startTime = timestamp(startYear, startMonth, startDay, 0, 0)
endTime = timestamp(endYear, endMonth, endDay, 23, 59)
inBacktest = enableBacktest ? (time >= startTime and time <= endTime) : true

utcHour = hour(time, "UTC")
utcDOW = dayofweek(time, "UTC")

asiaOK = enableAsia and (utcHour >= 1 and utcHour < 9)    // UTC+8 09:00-17:00
londonOK = enableLondon and (utcHour >= 8 and utcHour < 16)
usOK = enableUS and (utcHour >= 14 and utcHour < 21)       // 简化
inSession = not enableSession or (asiaOK or londonOK or usOK)

isWeekend = enableWeekendFilter and (utcDOW == dayofweek.saturday or utcDOW == dayofweek.sunday)
isFriNight = enableFridayNight and (utcDOW == dayofweek.friday and utcHour >= 18)

atrp = ta.atr(atrLen) / close * 100
extremeVolBlock = atrp > (atrPctTh * extremeVolMult)

isTimeAllowed = inBacktest and inSession and not isWeekend and not isFriNight and not extremeVolBlock

//=== 通用计算模块 ===
// 均线系统
ma1 = ta.sma(close, 30)
ma2 = ta.sma(close, 45)
ma3 = ta.sma(close, 60)
plot(ma1, color=color.blue, title="SMA30")
plot(ma2, color=color.orange, title="SMA45")
plot(ma3, color=color.purple, title="SMA60")

// 信号生成（加入时间与极端波动过滤）
isAboveMA = close > ma1 and close > ma2 and close > ma3
isBelowMA = close < ma1 and close < ma2 and close < ma3

rawLong = ta.barssince(isAboveMA) < confirmBars and (tradeDirection == "仅多" or tradeDirection == "多空双向")
rawShort = ta.barssince(isBelowMA) < confirmBars and (tradeDirection == "仅空" or tradeDirection == "多空双向")

longSignal = rawLong and isTimeAllowed
shortSignal = rawShort and isTimeAllowed

//=== 杠杆计算函数 ===
getLeverage() =>
    [newLeverage, newStep] = switch strategyType
        "雨后的夏天" =>
            lev = dynamicLeverage * 0.8
            lev := math.max(lev, 1.2)
            [lev, na]
        "午饭浮云" =>
            steps = array.from(50, 25, 15, 10, 8, 6, 5, 4, 3, 2, 1.2)
            step = math.min(leverageStep + 1, array.size(steps) - 1)
            [array.get(steps, step), step]
        => 
            [dynamicLeverage, na]

//=== 仓位计算函数（修正为返回“权益百分比”0~maxPositionPercent）===
getPositionPercent() =>
    basePct = switch strategyType
        "Fil浮盈"    => (compoundEquity > 0 ? capitalPercent * 100 : 0)
        "李法师Tony" => 10.0
        "肥宅"       => 2.0
        "午饭维持"   => 0.0  // 单独维持，不走百分比开仓
        => capitalPercent * 100 * (dynamicLeverage / 50.0)  // 以50为参考基准

    // 限幅
    math.min(math.max(basePct, 0), maxPositionPercent)

// 百分比转数量
pctToQty(pct) => pct <= 0 ? 0.0 : strategy.equity * (pct / 100.0) / close

//=== 订单执行逻辑 ===
// 单次允许多次进场的上限（按策略类型）
maxEntries = strategyType == "肥宅" ? 100 : strategyType == "雨后的夏天" ? 10 : strategyType == "午饭浮云" ? 10 : 1

// 主入场信号（按百分比开仓，并受最大总仓位上限约束）
if (longSignal or shortSignal) and entryCount < maxEntries
    basePct = getPositionPercent()
    curPct = strategy.equity > 0 ? math.abs(strategy.position_size) * close / strategy.equity * 100.0 : 0.0
    grantPct = math.max(0.0, math.min(basePct, maxPositionPercent - curPct))

    if grantPct > 0
        if longSignal
            strategy.entry("MainLong", strategy.long, qty=pctToQty(grantPct))
        if shortSignal
            strategy.entry("MainShort", strategy.short, qty=pctToQty(grantPct))

        // 更新杠杆状态（仅在实际下单时推进）
        [newLev, newStep] = getLeverage()
        dynamicLeverage := newLev
        if not na(newStep)
            leverageStep := newStep

        entryCount += 1
        positionActive := true

// 肥宅高频加仓（仅多、受上限约束、且需已有多头持仓）
if strategyType == "肥宅" and strategy.position_size > 0 and ta.change(close) > 0.01
    curPct = strategy.equity > 0 ? math.abs(strategy.position_size) * close / strategy.equity * 100.0 : 0.0
    addPct = math.min(2.0, math.max(0.0, maxPositionPercent - curPct))  // 每次最多再加2%
    if addPct > 0
        strategy.order("AddLong", strategy.long, qty=pctToQty(addPct))

// 复利模式更新（有持仓时更新基数，多空均可）
if strategyType == "Fil浮盈" and strategy.position_size != 0
    compoundEquity := strategy.equity

// 强制维持仓位（关闭或限制，以免与百分比风控冲突）
if strategyType == "午饭维持"
    // 这里仅演示：若无仓且允许交易，则以10%开多一次，之后不强制维持
    if strategy.position_size == 0 and longSignal
        strategy.entry("FillOnce", strategy.long, qty=pctToQty(10))

// 统一止损（多空对称），策略类型可叠加基础止损
if positionActive and strategy.position_size != 0
    if strategy.position_size > 0
        stopPrice := strategy.position_avg_price * (1 - stopLossPerc)
        strategy.exit("StopLossLong", "MainLong", stop=stopPrice)
    if strategy.position_size < 0
        stopPrice := strategy.position_avg_price * (1 + stopLossPerc)
        strategy.exit("StopLossShort", "MainShort", stop=stopPrice)

//=== 可视化 ===
plot(strategy.equity, title="资产曲线", color=color.green)
plot(dynamicLeverage, title="动态杠杆(内部参考)", color=color.red)
plotchar(longSignal, char='▲', location=location.belowbar, color=color.lime, title='Long')
plotchar(shortSignal, char='▼', location=location.abovebar, color=color.red, title='Short')
plotchar(isTimeAllowed, char='T', location=location.bottom, color=color.orange, title='时间允许')
plotchar(extremeVolBlock, char='X', location=location.bottom, color=color.maroon, title='极端波动阻断')