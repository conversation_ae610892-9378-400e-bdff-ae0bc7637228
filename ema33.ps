//@version=6

//声明==========================================================================
strategy(title = 'EMA33多空版', overlay = true, precision = 2, pyramiding = 1, initial_capital = 10000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, slippage = 0, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

//设置==========================================================================
// 基础参数
ma1_input = input.int(33, 'MA1', step = 1)
ma2_input = input.int(33, 'MA2', step = 1)
oppercent_1 = input.float(1.0, '突破MA1偏离度[%]', step = 0.1)
slpercent_1 = input.float(2.0, '止损[%]', step = 0.1)

// ======== 📈 交易方向设置 ========
group_direction = "📈 交易方向设置"
enable_long = input.bool(true, '启用做多', group = group_direction)
enable_short = input.bool(false, '启用做空', group = group_direction)

// ======== 🎯 止盈设置 ========
group_tp = "🎯 止盈设置"
tp_mode = input.string("信号止盈", "止盈模式", options=["信号止盈", "固定止盈", "分阶段止盈"], group = group_tp)
fixed_tp_percent = input.float(3.0, '固定止盈[%]', step = 0.1, minval = 0.5, maxval = 20.0, group = group_tp)

// 分阶段止盈参数
stage_tp_1_percent = input.float(1.5, '第一阶段止盈[%]', step = 0.1, minval = 0.5, maxval = 10.0, group = group_tp)
stage_tp_1_qty = input.float(50.0, '第一阶段平仓比例[%]', step = 5.0, minval = 10.0, maxval = 90.0, group = group_tp)
stage_tp_2_percent = input.float(3.0, '第二阶段止盈[%]', step = 0.1, minval = 1.0, maxval = 20.0, group = group_tp)
stage_tp_2_qty = input.float(100.0, '第二阶段平仓比例[%]', step = 5.0, minval = 10.0, maxval = 100.0, group = group_tp)

//定义==========================================================================
// EMA通道定义
ma1 = ta.ema(high, ma1_input)  // 上轨
ma2 = ta.ema(low, ma2_input)   // 下轨
// ma1   = ta.sma(close, ma1_input)
// ma2   = ta.sma(close,  ma2_input)

// 做多突破线（上轨+偏离度）
ma1op = ma1 * (1 + oppercent_1 / 100)
// 做空突破线（下轨-偏离度）
ma2op = ma2 * (1 - oppercent_1 / 100)

// 持仓信息
lastEntryPrice = strategy.opentrades.entry_price(strategy.opentrades - 1)
position_size = strategy.position_size
avg_price = strategy.position_avg_price

// 止损价格计算
lastEntryPrice_sl_long = lastEntryPrice * (1 - slpercent_1 / 100)  // 做多止损
lastEntryPrice_sl_short = lastEntryPrice * (1 + slpercent_1 / 100) // 做空止损

// 止盈价格计算
// 固定止盈
fixed_tp_long = avg_price * (1 + fixed_tp_percent / 100)
fixed_tp_short = avg_price * (1 - fixed_tp_percent / 100)

// 分阶段止盈
stage_tp_1_long = avg_price * (1 + stage_tp_1_percent / 100)
stage_tp_1_short = avg_price * (1 - stage_tp_1_percent / 100)
stage_tp_2_long = avg_price * (1 + stage_tp_2_percent / 100)
stage_tp_2_short = avg_price * (1 - stage_tp_2_percent / 100)

// 分阶段止盈状态跟踪
var bool stage1_executed = false
var bool stage2_executed = false

// 重置分阶段状态
if position_size == 0
    stage1_executed := false
    stage2_executed := false

//条件==========================================================================
// 做多信号
long = close > ma1op and enable_long
long_tp = close < ma2

// 做空信号
short = close < ma2op and enable_short
short_tp = close > ma1

// 止盈条件判断
// 固定止盈条件
fixed_tp_long_condition = position_size > 0 and close >= fixed_tp_long
fixed_tp_short_condition = position_size < 0 and close <= fixed_tp_short

// 分阶段止盈条件
stage_tp_1_long_condition = position_size > 0 and close >= stage_tp_1_long and not stage1_executed
stage_tp_1_short_condition = position_size < 0 and close <= stage_tp_1_short and not stage1_executed
stage_tp_2_long_condition = position_size > 0 and close >= stage_tp_2_long and not stage2_executed
stage_tp_2_short_condition = position_size < 0 and close <= stage_tp_2_short and not stage2_executed

// 综合止盈条件
should_tp_long = tp_mode == "信号止盈" ? long_tp :
                 tp_mode == "固定止盈" ? fixed_tp_long_condition : false

should_tp_short = tp_mode == "信号止盈" ? short_tp :
                  tp_mode == "固定止盈" ? fixed_tp_short_condition : false

//显示==========================================================================
// EMA通道显示
stLongplot=plot(ma1, color = color.new(color.white, 0), title = 'EMA上轨')
stShortplot=plot(ma2, color = color.new(color.red, 0), title = 'EMA下轨')
fill(stLongplot,stShortplot,color= color.rgb(0,255,0,0.1))

// 突破线显示
plot(ma1op, color = color.new(color.green, 50), style = plot.style_line, linewidth = 1, title = '做多突破线')
plot(ma2op, color = color.new(color.red, 50), style = plot.style_line, linewidth = 1, title = '做空突破线')

// 持仓信息显示
plot(lastEntryPrice, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '开仓价')
plot(position_size > 0 ? lastEntryPrice_sl_long : position_size < 0 ? lastEntryPrice_sl_short : na,
     color = color.new(color.red, 80), style = plot.style_linebr, linewidth = 1, title = '止损价')

// 止盈线显示
tp_color_long = color.new(color.blue, 60)
tp_color_short = color.new(color.orange, 60)

// 固定止盈线
plot(tp_mode == "固定止盈" and position_size > 0 ? fixed_tp_long : na,
     color = tp_color_long, style = plot.style_linebr, linewidth = 1, title = '做多止盈线')
plot(tp_mode == "固定止盈" and position_size < 0 ? fixed_tp_short : na,
     color = tp_color_short, style = plot.style_linebr, linewidth = 1, title = '做空止盈线')

// 分阶段止盈线
plot(tp_mode == "分阶段止盈" and position_size > 0 and not stage1_executed ? stage_tp_1_long : na,
     color = color.new(color.blue, 40), style = plot.style_linebr, linewidth = 1, title = '第一阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size > 0 and not stage2_executed ? stage_tp_2_long : na,
     color = color.new(color.blue, 60), style = plot.style_linebr, linewidth = 1, title = '第二阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size < 0 and not stage1_executed ? stage_tp_1_short : na,
     color = color.new(color.orange, 40), style = plot.style_linebr, linewidth = 1, title = '第一阶段止盈')
plot(tp_mode == "分阶段止盈" and position_size < 0 and not stage2_executed ? stage_tp_2_short : na,
     color = color.new(color.orange, 60), style = plot.style_linebr, linewidth = 1, title = '第二阶段止盈')

// 信号标记
plotshape(long, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small, title="做多信号")
plotshape(short, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small, title="做空信号")

//回测==========================================================================
ACT_BT = input.bool(true, title = '回测开关', group = '回测')
testStartYear = input.int(2023, title = '开始年份', minval = 1990, maxval = 2222, group = '回测')
testStartMonth = input.int(01, title = '开始月份', minval = 1, maxval = 12, group = '回测')
testStartDay = input.int(01, title = '开始日期', minval = 1, maxval = 31, group = '回测')
testPeriodStart = timestamp(testStartYear, testStartMonth, testStartDay, 0, 0)
testStopYear = input.int(2030, title = '终止年份', minval = 1980, maxval = 2222, group = '回测')
testStopMonth = input.int(12, title = '终止月份', minval = 1, maxval = 12, group = '回测')
testStopDay = input.int(31, title = '终止日期', minval = 1, maxval = 31, group = '回测')
testPeriodStop = timestamp(testStopYear, testStopMonth, testStopDay, 0, 0)
testPeriod = time >= testPeriodStart and time <= testPeriodStop ? true : false

//警报==========================================================================
alert_open_long = '做多开仓 bot '
alert_open_short = '做空开仓 bot '
alert_close = '平仓 bot '
alert_tp_stage1 = '第一阶段止盈 bot '
alert_tp_stage2 = '第二阶段止盈 bot '

//策略==========================================================================

if ACT_BT and testPeriod

    // ======== 开仓逻辑 ========
    // 做多开仓
    if long and strategy.position_size == 0
        strategy.entry('开多', strategy.long, comment = '📈做多', alert_message = alert_open_long)

    // 做空开仓
    if short and strategy.position_size == 0
        strategy.entry('开空', strategy.short, comment = '📉做空', alert_message = alert_open_short)

    // ======== 止损逻辑 ========
    // 做多止损
    strategy.exit('多止损', '开多', stop = lastEntryPrice_sl_long, comment = '❌多止损', alert_message = alert_close)

    // 做空止损
    strategy.exit('空止损', '开空', stop = lastEntryPrice_sl_short, comment = '❌空止损', alert_message = alert_close)

    // ======== 止盈逻辑 ========
    // 信号止盈和固定止盈
    if should_tp_long
        strategy.close('开多', comment = tp_mode == "信号止盈" ? '✅信号止盈' : '✅固定止盈', alert_message = alert_close)

    if should_tp_short
        strategy.close('开空', comment = tp_mode == "信号止盈" ? '✅信号止盈' : '✅固定止盈', alert_message = alert_close)

    // 分阶段止盈逻辑
    if tp_mode == "分阶段止盈"
        // 做多分阶段止盈
        if stage_tp_1_long_condition and not stage1_executed
            strategy.close('开多', qty_percent = stage_tp_1_qty, comment = '✅第一阶段止盈', alert_message = alert_tp_stage1)
            stage1_executed := true

        if stage_tp_2_long_condition and not stage2_executed
            strategy.close('开多', qty_percent = stage_tp_2_qty, comment = '✅第二阶段止盈', alert_message = alert_tp_stage2)
            stage2_executed := true

        // 做空分阶段止盈
        if stage_tp_1_short_condition and not stage1_executed
            strategy.close('开空', qty_percent = stage_tp_1_qty, comment = '✅第一阶段止盈', alert_message = alert_tp_stage1)
            stage1_executed := true

        if stage_tp_2_short_condition and not stage2_executed
            strategy.close('开空', qty_percent = stage_tp_2_qty, comment = '✅第二阶段止盈', alert_message = alert_tp_stage2)
            stage2_executed := true
