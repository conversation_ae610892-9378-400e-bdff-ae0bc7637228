Stack trace:
Frame         Function      Args
0007FFFFB690  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB690, 0007FFFFA590) msys-2.0.dll+0x2118E
0007FFFFB690  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB690  0002100469F2 (00021028DF99, 0007FFFFB548, 0007FFFFB690, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB690  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB690  00021006A545 (0007FFFFB6A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBBB240000 ntdll.dll
7FFBBA490000 KERNEL32.DLL
7FFBB8A00000 KERNELBASE.dll
7FFBBA680000 USER32.dll
7FFBB86D0000 win32u.dll
7FFBBA830000 GDI32.dll
7FFBB8860000 gdi32full.dll
7FFBB8700000 msvcp_win.dll
7FFBB8F00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBBAA40000 advapi32.dll
7FFBBB140000 msvcrt.dll
7FFBB9350000 sechost.dll
7FFBBA550000 RPCRT4.dll
7FFBB7ED0000 CRYPTBASE.DLL
7FFBB8980000 bcryptPrimitives.dll
7FFBBAA00000 IMM32.DLL
