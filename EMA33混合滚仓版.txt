//@version=6

// EMA33 混合滚仓版（账户级动态滚仓 A + 单笔魔鬼加仓 B）
// - A：当账户总收益达到阈值，放大“下一笔”的仓位（复利、稳健）
// - B：当当前持仓达到分档浮盈，立刻在“该笔交易内”加仓（趋势爆发、激进）
// - Hybrid：根据趋势强弱（EMA斜率% + ATR%）自动切换是否启用 B，并用 A 对 B 进行资金上限约束

strategy(title = 'EMA33混合滚仓版', overlay = true, pyramiding = 12, precision = 2, initial_capital = 10000, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

// ========= 基础信号 =========
ma_len = input.int(33, 'EMA长度', minval = 3)
open_dev_long = input.float(1.0, '做多偏离[%] (收盘高于EMA)', step = 0.1)
open_dev_short = input.float(1.0, '做空偏离[%] (收盘低于EMA)', step = 0.1)
sl_percent = input.float(2.0, '基础止损[%]', step = 0.1, minval = 0.1)

enable_long = input.bool(true, '启用做多')
enable_short = input.bool(true, '启用做空')

// ========= 📅 回测时间设定 =========
group_backtest = "📅 回测时间设定"
enable_backtest_period = input.bool(true, '启用回测时间限制', group = group_backtest)
start_year = input.int(2022, '开始年份', minval = 2020, maxval = 2030, group = group_backtest)
start_month = input.int(1, '开始月份', minval = 1, maxval = 12, group = group_backtest)
start_day = input.int(1, '开始日期', minval = 1, maxval = 31, group = group_backtest)
end_year = input.int(2024, '结束年份', minval = 2020, maxval = 2030, group = group_backtest)
end_month = input.int(12, '结束月份', minval = 1, maxval = 12, group = group_backtest)
end_day = input.int(31, '结束日期', minval = 1, maxval = 31, group = group_backtest)

start_time = timestamp(start_year, start_month, start_day, 0, 0)
end_time = timestamp(end_year, end_month, end_day, 23, 59)
in_backtest_period = enable_backtest_period ? (time >= start_time and time <= end_time) : true

// ========= ⏰ 时间风控系统 =========
group_time_control = "⏰ 时间风控系统"
enable_session_filter = input.bool(true, '启用交易时段过滤', group = group_time_control)
enable_asia_session = input.bool(true, '亚洲时段 (UTC+8: 09:00-17:00)', group = group_time_control)
enable_london_session = input.bool(true, '伦敦时段 (UTC+0: 08:00-16:00)', group = group_time_control)
enable_us_session = input.bool(true, '美国时段 (UTC-5: 09:30-16:00)', group = group_time_control)
enable_weekend_filter = input.bool(true, '过滤周末', group = group_time_control)
enable_friday_night_filter = input.bool(true, '过滤周五晚间 (18:00后)', group = group_time_control)

// 时区转换函数
get_utc_hour() => hour(time, "UTC")
get_utc_day() => dayofweek(time, "UTC")

// 交易时段判断
is_asia_time = enable_asia_session and (get_utc_hour() >= 1 and get_utc_hour() < 9)  // UTC+8 09:00-17:00 = UTC 01:00-09:00
is_london_time = enable_london_session and (get_utc_hour() >= 8 and get_utc_hour() < 16)  // UTC 08:00-16:00
is_us_time = enable_us_session and (get_utc_hour() >= 14 and get_utc_hour() < 21)  // UTC-5 09:30-16:00 = UTC 14:30-21:00

is_valid_session = not enable_session_filter or (is_asia_time or is_london_time or is_us_time)

// 周末和周五晚间过滤
is_weekend = enable_weekend_filter and (get_utc_day() == dayofweek.saturday or get_utc_day() == dayofweek.sunday)
is_friday_night = enable_friday_night_filter and (get_utc_day() == dayofweek.friday and get_utc_hour() >= 18)

is_time_allowed = in_backtest_period and is_valid_session and not is_weekend and not is_friday_night

ema = ta.ema(close, ma_len)
long_signal = enable_long and close > ema * (1 + open_dev_long / 100) and is_time_allowed
short_signal = enable_short and close < ema * (1 - open_dev_short / 100) and is_time_allowed

// ========= Hybrid 开关（行情强弱识别） =========
enable_hybrid = input.bool(true, '启用混合模式(Hybrid)')
ema_slope_len = input.int(14, 'EMA斜率窗口', minval = 1)
slope_th = input.float(0.8, '斜率阈值[%]', step = 0.1)
atrp_th = input.float(5.0, 'ATR比例阈值[%]', step = 0.1)

slope_pct = math.abs(ta.change(ema, ema_slope_len)) / ema * 100
atrp = ta.atr(14) / close * 100
strong_trend = (slope_pct > slope_th) and (atrp > atrp_th)

// ========= A：账户级动态杠杆滚仓 =========
// 含义：总收益达到阈值 → 放大“下一笔”仓位（不在当前持仓加仓）

group_A = 'A 账户级动态滚仓'
enable_A = input.bool(true, '启用A-账户滚仓', group = group_A)
A_initial_pos_pct = input.float(10.0, 'A 初始仓位[%]', step = 1, minval = 1, maxval = 50, group = group_A)
A_roll_th = input.float(30.0, 'A 滚仓触发盈利[%]', step = 5, minval = 10, maxval = 200, group = group_A)
A_protect_pct = input.float(50.0, 'A 利润保护[%]', step = 5, minval = 20, maxval = 80, group = group_A)
A_max_lev = input.float(3.0, 'A 最大杠杆倍数', step = 0.5, minval = 1, maxval = 10, group = group_A)
A_inc_pct = input.float(70.0, 'A 每轮增幅[%]', step = 5, minval = 30, maxval = 100, group = group_A)
A_max_roll = input.int(5, 'A 最大滚仓次数', minval = 1, maxval = 10, group = group_A)

var bool A_active = false
var int A_roll_count = 0
var float A_lev = 1.0
var float A_last_base_eq = 0.0
var float A_protected_equity = 0.0

if barstate.isfirst
    A_last_base_eq := strategy.initial_capital

A_total_profit_pct = strategy.initial_capital > 0 ? (strategy.equity - strategy.initial_capital) / strategy.initial_capital * 100 : 0.0

// 触发首轮滚仓
if enable_A and not A_active and A_total_profit_pct >= A_roll_th
    A_active := true
    A_roll_count := 1
    A_last_base_eq := strategy.equity
    A_protected_equity := strategy.equity * (A_protect_pct / 100)
    A_lev := math.min(A_lev * 1.5, A_max_lev)

// 继续滚仓
if enable_A and A_active and A_roll_count < A_max_roll
    A_leg_profit = A_last_base_eq > 0 ? (strategy.equity - A_last_base_eq) / A_last_base_eq * 100 : 0.0
    if A_leg_profit >= A_roll_th
        A_roll_count += 1
        A_last_base_eq := strategy.equity
        A_protected_equity := strategy.equity * (A_protect_pct / 100)
        A_lev := math.min(A_lev * 1.2, A_max_lev)

A_base_pos_pct = enable_A ? A_initial_pos_pct : 100.0
A_dynamic_pos_pct = A_active ? math.min(A_base_pos_pct * A_lev * (A_inc_pct / 100.0), 100.0) : A_base_pos_pct

// ========= B：魔鬼滚仓（金字塔加仓） =========
// 含义：当前持仓达到档位浮盈 → 立即加仓（集中火力抓趋势）

group_B = 'B 魔鬼滚仓'
enable_B = input.bool(true, '启用B-魔鬼滚仓', group = group_B)
B_base_pos_pct = input.float(10.0, 'B 基础仓位[%]', step = 1, minval = 1, maxval = 50, group = group_B)

B_th1 = input.float(20.0, 'B 第一档盈利[%]', step = 5, minval = 5, maxval = 100, group = group_B)
B_mul1 = input.float(1.5, 'B 第一档倍数', step = 0.1, minval = 1.0, maxval = 5.0, group = group_B)
B_th2 = input.float(50.0, 'B 第二档盈利[%]', step = 5, minval = 20, maxval = 200, group = group_B)
B_mul2 = input.float(2.0, 'B 第二档倍数', step = 0.1, minval = 1.5, maxval = 8.0, group = group_B)
B_th3 = input.float(100.0, 'B 雪崩档盈利[%]', step = 10, minval = 50, maxval = 500, group = group_B)
B_mul3 = input.float(3.0, 'B 雪崩档倍数', step = 0.5, minval = 2.0, maxval = 15.0, group = group_B)

B_max_total_pos = input.float(400.0, 'B 最大总仓位上限[%]', step = 50, minval = 100, maxval = 1000, group = group_B)
B_emergency_stop = input.float(-25.0, 'B 紧急止损阈值[%]', step = 1, minval = -80, maxval = -5, group = group_B)
B_cooldown_bars = input.int(6, 'B 冷却根数(6H根)', minval = 1, maxval = 50, group = group_B)

// A 对 B 的上限约束倍率
hybrid_cap_mult = input.float(1.0, 'Hybrid对B上限倍率', step = 0.1, minval = 0.5, maxval = 2.0)

// B 状态变量
var bool B_lvl1_done = false
var bool B_lvl2_done = false
var bool B_lvl3_done = false
var int B_cooldown = 0

B_cooldown := math.max(0, B_cooldown - 1)
allow_devil = enable_hybrid and enable_B and strong_trend and (B_cooldown == 0)

// ========= 位置/额度换算 =========
percent_to_qty(pct) => pct <= 0 ? 0.0 : strategy.equity * pct / 100.0 / close
current_pos_pct = strategy.equity > 0 ? math.abs(strategy.position_size) * close / strategy.equity * 100.0 : 0.0

account_cap = A_dynamic_pos_pct
// 若允许B，则有效上限 = min(A给的上限 * 倍率, B的总上限)
B_cap = allow_devil ? account_cap * hybrid_cap_mult : 0.0
effective_cap = allow_devil ? math.min(B_cap, B_max_total_pos) : account_cap
remaining_pct = math.max(0.0, effective_cap - current_pos_pct)

// ========= 入场（首单用 A 的上限；B 仅在持仓后生效） =========
if strategy.position_size == 0
    if long_signal
        base_pct = math.min(A_dynamic_pos_pct, effective_cap)
        strategy.entry('开多', strategy.long, qty = percent_to_qty(base_pct), comment = '📈首单多')
        B_lvl1_done := false, B_lvl2_done := false, B_lvl3_done := false
    else if short_signal
        base_pct = math.min(A_dynamic_pos_pct, effective_cap)
        strategy.entry('开空', strategy.short, qty = percent_to_qty(base_pct), comment = '📉首单空')
        B_lvl1_done := false, B_lvl2_done := false, B_lvl3_done := false

// ========= B：加仓执行（受 effective_cap 约束） =========
pos_dir = strategy.position_size > 0 ? 1 : strategy.position_size < 0 ? -1 : 0
avg_price = strategy.position_avg_price
cur_profit_pct = (pos_dir != 0 and avg_price > 0) ? (close - avg_price) / avg_price * 100 * pos_dir : 0.0

req1 = B_base_pos_pct * B_mul1
req2 = B_base_pos_pct * B_mul2
req3 = B_base_pos_pct * B_mul3

can_add1 = allow_devil and (pos_dir != 0) and not B_lvl1_done and (cur_profit_pct >= B_th1) and (remaining_pct > 0)
can_add2 = allow_devil and (pos_dir != 0) and not B_lvl2_done and (cur_profit_pct >= B_th2) and (remaining_pct > 0)
can_add3 = allow_devil and (pos_dir != 0) and not B_lvl3_done and (cur_profit_pct >= B_th3) and (remaining_pct > 0)

if can_add1
    grant = math.min(req1, remaining_pct)
    if grant > 0
        if pos_dir > 0
            strategy.entry('⚡加仓多1', strategy.long, qty = percent_to_qty(grant), comment = '⚡B1')
        else
            strategy.entry('⚡加仓空1', strategy.short, qty = percent_to_qty(grant), comment = '⚡B1')
        B_lvl1_done := true

// 更新剩余额度（近似，次bar再精准）
current_pos_pct := strategy.equity > 0 ? math.abs(strategy.position_size) * close / strategy.equity * 100.0 : current_pos_pct
remaining_pct := math.max(0.0, effective_cap - current_pos_pct)

if can_add2
    grant = math.min(req2, remaining_pct)
    if grant > 0
        if pos_dir > 0
            strategy.entry('🚀加仓多2', strategy.long, qty = percent_to_qty(grant), comment = '🚀B2')
        else
            strategy.entry('🚀加仓空2', strategy.short, qty = percent_to_qty(grant), comment = '🚀B2')
        B_lvl2_done := true

current_pos_pct := strategy.equity > 0 ? math.abs(strategy.position_size) * close / strategy.equity * 100.0 : current_pos_pct
remaining_pct := math.max(0.0, effective_cap - current_pos_pct)

if can_add3
    grant = math.min(req3, remaining_pct)
    if grant > 0
        if pos_dir > 0
            strategy.entry('💥雪崩多', strategy.long, qty = percent_to_qty(grant), comment = '💥B3')
        else
            strategy.entry('💥雪崩空', strategy.short, qty = percent_to_qty(grant), comment = '💥B3')
        B_lvl3_done := true

// ========= 基础止损（固定SL），紧急止损（B专用） =========
long_sl = strategy.position_avg_price * (1.0 - sl_percent / 100.0)
short_sl = strategy.position_avg_price * (1.0 + sl_percent / 100.0)
if pos_dir > 0
    strategy.exit('多止损', '开多', stop = long_sl, comment = '❌SL多')
else if pos_dir < 0
    strategy.exit('空止损', '开空', stop = short_sl, comment = '❌SL空')

// 紧急止损（基于浮盈/亏%），触发后冷却
if enable_B and (pos_dir != 0) and (cur_profit_pct <= B_emergency_stop)
    strategy.close_all(comment = '🔥紧急止损')
    B_cooldown := B_cooldown_bars

// 极端波动过滤（可选）：当 ATR% 远超阈值上沿，暂停新开仓与加仓
extreme_vol_mult = input.float(2.0, '极端波动系数(×ATR阈值)', step = 0.1, minval = 1.0)
extreme_vol_block = atrp > (atrp_th * extreme_vol_mult)
if extreme_vol_block
    // 关闭首单触发 & B 加仓（仅影响当根信号）
    long_signal := false
    short_signal := false
    allow_devil := false

// ========= 反向信号平仓（可选） =========
if pos_dir > 0 and short_signal
    strategy.close('开多', comment = '↔ 反向信号平多')
    B_lvl1_done := false, B_lvl2_done := false, B_lvl3_done := false
if pos_dir < 0 and long_signal
    strategy.close('开空', comment = '↔ 反向信号平空')
    B_lvl1_done := false, B_lvl2_done := false, B_lvl3_done := false

// ========= 可视化 =========
plot(ema, color = color.new(color.yellow, 0), title = 'EMA')
plotchar(long_signal, char = '▲', location = location.belowbar, color = color.new(color.lime, 0), size = size.tiny, title = '做多信号')
plotchar(short_signal, char = '▼', location = location.abovebar, color = color.new(color.red, 0), size = size.tiny, title = '做空信号')

plotchar(strong_trend, char = 'T', location = location.bottom, color = color.orange, size = size.tiny, title = '强趋势')
plotchar(allow_devil, char = 'B', location = location.bottom, color = color.new(color.red, 0), size = size.tiny, title = '允许B')

plotchar(B_lvl1_done, char = '⚡', location = location.belowbar, color = color.yellow, size = size.tiny, title = 'B1执行')
plotchar(B_lvl2_done, char = '🚀', location = location.belowbar, color = color.orange, size = size.tiny, title = 'B2执行')
plotchar(B_lvl3_done, char = '💥', location = location.belowbar, color = color.red, size = size.tiny, title = 'B3执行')

// 信息面板（简版）
var table info = table.new(position.top_right, 3, 7, bgcolor = color.new(color.white, 85))
if barstate.islast
    table.cell(info, 0, 0, 'Hybrid 状态', text_color = color.blue)
    table.cell(info, 1, 0, enable_hybrid ? 'ON' : 'OFF', text_color = enable_hybrid ? color.green : color.red)
    table.cell(info, 2, 0, strong_trend ? '强趋势' : '普通', text_color = strong_trend ? color.orange : color.gray)

    table.cell(info, 0, 1, 'A 动态仓位', text_color = color.black)
    table.cell(info, 1, 1, str.tostring(A_dynamic_pos_pct, '#.#') + '%', text_color = color.black)
    table.cell(info, 2, 1, 'roll:' + str.tostring(A_roll_count), text_color = A_active ? color.yellow : color.gray)

    table.cell(info, 0, 2, 'B 有效上限', text_color = color.black)
    table.cell(info, 1, 2, str.tostring(effective_cap, '#.#') + '%', text_color = color.black)
    table.cell(info, 2, 2, '剩余' + str.tostring(remaining_pct, '#.#') + '%', text_color = remaining_pct > 0 ? color.green : color.red)

    table.cell(info, 0, 3, '当前仓位', text_color = color.black)
    table.cell(info, 1, 3, str.tostring(current_pos_pct, '#.#') + '%', text_color = color.black)
    table.cell(info, 2, 3, pos_dir > 0 ? '多' : pos_dir < 0 ? '空' : '无', text_color = pos_dir > 0 ? color.lime : pos_dir < 0 ? color.red : color.gray)

    table.cell(info, 0, 4, '浮盈%', text_color = color.black)
    table.cell(info, 1, 4, str.tostring(cur_profit_pct, '#.##') + '%', text_color = cur_profit_pct >= 0 ? color.green : color.red)
    table.cell(info, 2, 4, B_cooldown > 0 ? ('冷却' + str.tostring(B_cooldown)) : '就绪', text_color = B_cooldown > 0 ? color.orange : color.green)

    table.cell(info, 0, 5, 'Slope/ATR%', text_color = color.black)
    table.cell(info, 1, 5, str.tostring(slope_pct, '#.##') + '/' + str.tostring(atrp, '#.##') + '%', text_color = color.black)
    table.cell(info, 2, 5, strong_trend ? '强' : '弱', text_color = strong_trend ? color.orange : color.gray)

    table.cell(info, 0, 6, '总收益%', text_color = color.black)
    table.cell(info, 1, 6, str.tostring(A_total_profit_pct, '#.##') + '%', text_color = A_total_profit_pct >= 0 ? color.green : color.red)
    table.cell(info, 2, 6, '', text_color = color.black)

