//@version=6

//声明==========================================================================
strategy(title = 'EMA33v6', overlay = true, precision = 2, pyramiding = 1, initial_capital = 10000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, slippage = 0, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

//设置==========================================================================
// 主体策略参数
ma1_input = input.int(33, 'MA1', step = 1)
ma2_input = input.int(33, 'MA2', step = 1)
oppercent_1 = input.float(1.0, '突破MA1偏离度[%]', step = 0.1)
slpercent_1 = input.float(2.0, '止损[%]', step = 0.1)

// ======== ⏰ 时间风控系统 ========
group_time_control = "⏰ 时间风控系统"
enable_golden_time = input.bool(true, '启用黄金时段限制', group = group_time_control)
golden_start_hour = input.int(9, '黄金时段开始时间', minval = 0, maxval = 23, group = group_time_control)
golden_end_hour = input.int(11, '黄金时段结束时间', minval = 0, maxval = 23, group = group_time_control)
enable_us_session = input.bool(true, '启用美股时段', group = group_time_control)
enable_death_time = input.bool(true, '启用死亡时间限制', group = group_time_control)
avoid_friday_night = input.bool(true, '避开周五晚间', group = group_time_control)

// 交易时段（保留原有逻辑）
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '启用交易时段', group = group_trading_time)
asia_session = input.bool(true, '亚洲时段 09:00-13:00', group = group_trading_time)
london_session = input.bool(true, '伦敦时段 15:00-18:00', group = group_trading_time)
us_am_session = input.bool(true, '美国早盘 21:30-00:00', group = group_trading_time)
us_pm_session = input.bool(true, '美国下午 02:30-05:00', group = group_trading_time)
custom_session = input.bool(false, '启用自定义时段', group = group_trading_time)

// 自定义时段参数（仅在启用时显示）
custom_start_h = input.int(7, '开始小时', group = group_trading_time, minval=0, maxval=23, tooltip='仅在启用自定义时段时有效')
custom_start_m = input.int(0, '开始分钟', group = group_trading_time, minval=0, maxval=59)
custom_end_h = input.int(9, '结束小时', group = group_trading_time, minval=0, maxval=24)
custom_end_m = input.int(0, '结束分钟', group = group_trading_time, minval=0, maxval=59)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

//定义==========================================================================
// ======== 时间判断函数 ========
in_session(s_hour, s_minute, e_hour, e_minute) =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_h = hour(exchange_time)
    exchange_m = minute(exchange_time)

    // 处理跨日情况
    start_day_offset = s_hour < e_hour ? 0 : 1
    end_day_offset = s_hour < e_hour ? 0 : 1
    (exchange_h > s_hour or (exchange_h == s_hour and exchange_m >= s_minute)) and (exchange_h < e_hour or (exchange_h == e_hour and exchange_m <= e_minute))

// ======== 回测参数定义 ========
ACT_BT = input.bool(true, title = '回测开关', group = '回测')
testStartYear = input.int(2023, title = '开始年份', minval = 1990, maxval = 2222, group = '回测')
testStartMonth = input.int(01, title = '开始月份', minval = 1, maxval = 12, group = '回测')
testStartDay = input.int(01, title = '开始日期', minval = 1, maxval = 31, group = '回测')
testPeriodStart = timestamp(testStartYear, testStartMonth, testStartDay, 0, 0)
testStopYear = input.int(2030, title = '终止年份', minval = 1980, maxval = 2222, group = '回测')
testStopMonth = input.int(12, title = '终止月份', minval = 1, maxval = 12, group = '回测')
testStopDay = input.int(31, title = '终止日期', minval = 1, maxval = 31, group = '回测')
testPeriodStop = timestamp(testStopYear, testStopMonth, testStopDay, 0, 0)
testPeriod = time >= testPeriodStart and time <= testPeriodStop ? true : false

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// 夏令时判断函数
is_dst() =>
    year_var = year(time)
    // 3月的第二个星期日（夏令时开始）
    march_date = timestamp(year_var, 3, 8) // 从3月8日开始找
    march_day = dayofweek(march_date) == dayofweek.sunday ? march_date : march_date + (7 - dayofweek(march_date) + 1) * 86400000
    dst_start = march_day + (7 * 86400000) // 加7天得到第二个星期日

    // 11月的第一个星期日（夏令时结束）
    november_date = timestamp(year_var, 11, 1)
    november_day = dayofweek(november_date) == dayofweek.sunday ? november_date : november_date + (7 - dayofweek(november_date) + 1) * 86400000
    dst_end = november_day

    time >= dst_start and time < dst_end

// 修改美国时段判断逻辑
get_us_session() =>
    var int us_am_start_h = is_dst() ? 21 : 22  // 北京时间
    var int us_am_start_m = is_dst() ? 30 : 30
    var int us_am_end_h = is_dst() ? 4 : 5      // 次日凌晨
    var int us_pm_start_h = is_dst() ? 2 : 3    // 美国下午时段对应北京时间
    var int us_pm_start_m = 30
    var int us_pm_end_h = is_dst() ? 5 : 6

    // 美国早盘时段（包含跨日判断）
    us_am = us_am_session and (in_session(us_am_start_h, us_am_start_m, 24, 0) or in_session(0, 0, us_am_end_h, 0))

    // 美国下午时段（次日凌晨）
    us_pm = us_pm_session and in_session(us_pm_start_h, us_pm_start_m, us_pm_end_h, 0)

    us_am or us_pm

// 修改后的交易时段判断
in_trading_time() =>
    if not tradingtime
        true
    else
        asia = asia_session and in_session(9, 0, 13, 0)
        london = london_session and in_session(15, 0, 18, 0)
        us = get_us_session()  // 调用新的美国时段判断
        custom = custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)

        asia or london or us or custom

// 主体策略指标
ma1 = ta.ema(high, ma1_input)
ma2 = ta.ema(low, ma2_input)
// ma1   = ta.sma(close, ma1_input)
// ma2   = ta.sma(close,  ma2_input)
ma1op = ma1 * (1 + oppercent_1 / 100)
ma2op = ma2 * (1 + oppercent_1 / 100)
lastEntryPrice = strategy.opentrades.entry_price(strategy.opentrades - 1)
lastEntryPrice_sl = lastEntryPrice * (1 - slpercent_1 / 100)

//条件==========================================================================
// ======== 时间风控条件 ========
// 时间风控 - 检查是否在交易禁忌时间
current_hour = hour(time, "UTC+8")
current_day = dayofweek(time)
is_golden_time = enable_golden_time ? (current_hour >= golden_start_hour and current_hour <= golden_end_hour) : true
is_death_time = enable_death_time and avoid_friday_night and current_day == dayofweek.friday and current_hour >= 18

// 综合时间判断
is_valid_trading_time = in_trading_time() and is_golden_time and not is_death_time

// 主体策略信号
long = close > ma1op
long_tp = close < ma2
// long    = close > ma1op or close > ma2op
// long_tp = close < ma1 and close < ma2

// 最终交易条件（整合时间风控）
can_open_long = long and is_valid_trading_time

//显示==========================================================================
stLongplot=plot(ma1, color = color.new(color.white, 0), title = 'MA1')
stShortplot=plot(ma2, color = color.new(color.red, 0), title = 'MA2')
fill(stLongplot,stShortplot,color= color.rgb(0,255,0,0.1))
plot(lastEntryPrice, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '开仓价')
plot(lastEntryPrice_sl, color = color.new(color.red, 80), style = plot.style_linebr, linewidth = 1, title = '止损价')

// 时间风控状态标记
plotshape(not is_valid_trading_time and long, style=shape.xcross, location=location.abovebar, color=color.red, size=size.small, title="禁止交易时段")
plotshape(is_death_time, style=shape.diamond, location=location.abovebar, color=color.purple, size=size.normal, title="死亡时间")

// 时间状态信息面板
var table time_info_table = table.new(position.top_right, 2, 4, bgcolor=color.new(color.white, 80), border_width=1)
if barstate.islast
    table.cell(time_info_table, 0, 0, "⏰ 时间状态", text_color=color.blue, text_size=size.normal)
    table.cell(time_info_table, 1, 0, "", text_color=color.black)

    table.cell(time_info_table, 0, 1, "交易时段", text_color=color.black)
    trading_session_text = ""
    if asia_session and in_session(9, 0, 13, 0)
        trading_session_text := "亚洲"
    else if london_session and in_session(15, 0, 18, 0)
        trading_session_text := "伦敦"
    else if get_us_session()
        trading_session_text := "美股"
    else if custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)
        trading_session_text := "自定义"
    else
        trading_session_text := "休市"
    table.cell(time_info_table, 1, 1, trading_session_text, text_color=trading_session_text == "休市" ? color.gray : color.green)

    table.cell(time_info_table, 0, 2, "黄金时段", text_color=color.black)
    table.cell(time_info_table, 1, 2, is_golden_time ? "✅是" : "❌否", text_color=is_golden_time ? color.green : color.gray)

    table.cell(time_info_table, 0, 3, "可交易", text_color=color.black)
    table.cell(time_info_table, 1, 3, is_valid_trading_time ? "✅允许" : "❌禁止", text_color=is_valid_trading_time ? color.green : color.red)

//警报==========================================================================
alert_open = ' bot '

alert_close = ' bot '

//策略==========================================================================

if can_open_long and ACT_BT and testPeriod
    strategy.entry('开多', strategy.long, comment = '开仓' , alert_message = alert_open)
strategy.exit('止损', '开多', stop = lastEntryPrice_sl, comment = '❌' , alert_message = alert_close)

if long_tp
    strategy.close('开多', comment = '✅', alert_message = alert_close)
