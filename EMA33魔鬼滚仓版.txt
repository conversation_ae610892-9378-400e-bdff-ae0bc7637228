//@version=6

//声明==========================================================================
strategy(title = 'EMA33魔鬼滚仓版', overlay = true, precision = 2, pyramiding = 10, initial_capital = 10000, default_qty_type = strategy.fixed, default_qty_value = 1, slippage = 0, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

//设置==========================================================================
// 主体策略参数（保持不变）
ma1_input = input.int(33, 'MA1', step = 1)
ma2_input = input.int(33, 'MA2', step = 1)
oppercent_1 = input.float(1.0, '突破MA1偏离度[%]', step = 0.1)

// 魔鬼滚仓风控参数
group_risk = "🔥 魔鬼滚仓风控系统"
initial_risk_percent = input.float(5.0, '首单风险比例[%]', step = 0.5, minval = 1.0, maxval = 10.0, group = group_risk)
base_stop_percent = input.float(3.0, '基础止损[%]', step = 0.1, minval = 1.0, maxval = 10.0, group = group_risk)
enable_rolling = input.bool(true, '启用滚仓模式', group = group_risk)
max_pyramid_levels = input.int(5, '最大加仓次数', minval = 1, maxval = 10, group = group_risk)

// 动态止盈参数
group_tp = "📈 动态止盈系统"
trailing_profit_step = input.float(10.0, '止盈步长[%]', step = 1.0, minval = 5.0, maxval = 20.0, group = group_tp)
trailing_stop_ratio = input.float(0.7, '止损跟进比例', step = 0.1, minval = 0.5, maxval = 0.9, group = group_tp)
extreme_market_step = input.float(5.0, '极端行情步长[%]', step = 1.0, minval = 3.0, maxval = 10.0, group = group_tp)
extreme_market_ratio = input.float(0.8, '极端行情跟进比例', step = 0.1, minval = 0.6, maxval = 0.9, group = group_tp)

// 加仓触发条件
group_add = "⚡ 加仓触发系统"
profit_threshold_1 = input.float(20.0, '第一档盈利阈值[%]', step = 5.0, minval = 10.0, maxval = 50.0, group = group_add)
profit_threshold_2 = input.float(50.0, '第二档盈利阈值[%]', step = 5.0, minval = 30.0, maxval = 100.0, group = group_add)
profit_threshold_3 = input.float(100.0, '雪崩模式阈值[%]', step = 10.0, minval = 80.0, maxval = 200.0, group = group_add)
consecutive_wins_trigger = input.int(3, '连胜触发次数', minval = 2, maxval = 5, group = group_add)
volatility_threshold = input.float(15.0, '波动率阈值[%]', step = 1.0, minval = 10.0, maxval = 30.0, group = group_add)

// ======== 💰 物理隔离风控 ========
group_isolation = "💰 物理隔离风控"
enable_withdrawal_trigger = input.bool(true, '启用提现熔断', group = group_isolation)
daily_profit_limit = input.float(30.0, '当日盈利提现阈值[%]', step = 5.0, minval = 20.0, maxval = 100.0, group = group_isolation)
enable_freeze_mechanism = input.bool(true, '启用自毁机制', group = group_isolation)
max_consecutive_losses = input.int(3, '连败冻结次数', minval = 2, maxval = 5, group = group_isolation)
freeze_hours = input.int(24, '冻结时长[小时]', minval = 12, maxval = 72, group = group_isolation)

// ======== ⏰ 时间风控系统 ========
var group_time = "⏰ 时间风控系统"
enable_sessions = input.bool(true, '启用多会话限制', group = group_time)
timezone_offset = input.int(8, '时区偏移(小时)', minval=-12, maxval=14, group = group_time)

// 会话开关与时间
enable_asia = input.bool(true, '亚洲时段', group = group_time)
asia_start_h = input.int(9, '亚洲开始时', minval=0, maxval=23, group = group_time)
asia_start_m = input.int(0, '亚洲开始分', minval=0, maxval=59, group = group_time)
asia_end_h = input.int(13, '亚洲结束时', minval=0, maxval=24, group = group_time)
asia_end_m = input.int(0, '亚洲结束分', minval=0, maxval=59, group = group_time)

enable_london = input.bool(true, '伦敦时段', group = group_time)
london_start_h = input.int(15, '伦敦开始时', minval=0, maxval=23, group = group_time)
london_start_m = input.int(0, '伦敦开始分', minval=0, maxval=59, group = group_time)
london_end_h = input.int(18, '伦敦结束时', minval=0, maxval=24, group = group_time)
london_end_m = input.int(0, '伦敦结束分', minval=0, maxval=59, group = group_time)

enable_us_am = input.bool(true, '美股早盘', group = group_time)
us_am_start_h = input.int(21, '美股早盘开始时', minval=0, maxval=23, group = group_time)
us_am_start_m = input.int(30, '美股早盘开始分', minval=0, maxval=59, group = group_time)
us_am_end_h = input.int(0, '美股早盘结束时(跨日=0)', minval=0, maxval=24, group = group_time)
us_am_end_m = input.int(0, '美股早盘结束分', minval=0, maxval=59, group = group_time)

enable_us_pm = input.bool(true, '美股下午盘', group = group_time)
us_pm_start_h = input.int(2, '美股下午开始时', minval=0, maxval=23, group = group_time)
us_pm_start_m = input.int(30, '美股下午开始分', minval=0, maxval=59, group = group_time)
us_pm_end_h = input.int(5, '美股下午结束时', minval=0, maxval=24, group = group_time)
us_pm_end_m = input.int(0, '美股下午结束分', minval=0, maxval=59, group = group_time)

avoid_weekend = input.bool(false, '避开周末(周六/周日)', group = group_time)
enable_death_time = input.bool(true, '启用死亡时间限制(周五晚)', group = group_time)

// 极端波动过滤
enable_extreme_filter = input.bool(true, '避开极端波动入场', group = group_time)

// 统一时间工具：换算到目标时区
exchange_time = time + timezone_offset * 60 * 60 * 1000
ex_h = hour(exchange_time)
ex_m = minute(exchange_time)
ex_dow = dayofweek(exchange_time)

in_session(s_h, s_m, e_h, e_m) =>
    // 处理跨日会话：若开始时间大于结束时间，则视为跨越午夜
    after_start = (ex_h > s_h) or (ex_h == s_h and ex_m >= s_m)
    before_end  = (ex_h < e_h) or (ex_h == e_h and ex_m < e_m)
    s_h < e_h or (s_h == e_h and s_m < e_m) ? (after_start and before_end) : (after_start or before_end)

in_trading_time() =>
    not enable_sessions ? true : (
         (enable_asia   and in_session(as_h=asia_start_h, s_m=asia_start_m, e_h=asia_end_h, e_m=asia_end_m)) or
         (enable_london and in_session(s_h=london_start_h, s_m=london_start_m, e_h=london_end_h, e_m=london_end_m)) or
         (enable_us_am  and in_session(s_h=us_am_start_h,  s_m=us_am_start_m,  e_h=us_am_end_h,  e_m=us_am_end_m)) or
         (enable_us_pm  and in_session(s_h=us_pm_start_h,  s_m=us_pm_start_m,  e_h=us_pm_end_h,  e_m=us_pm_end_m))
    )

is_weekend = avoid_weekend and (ex_dow == dayofweek.saturday or ex_dow == dayofweek.sunday)
is_friday_night = (ex_dow == dayofweek.friday) and (ex_h >= 18)
is_death_time = enable_death_time and is_friday_night

can_trade_time = in_trading_time() and not is_weekend and not is_death_time

//定义==========================================================================
// 主体策略指标（保持不变）
ma1 = ta.ema(high, ma1_input)
ma2 = ta.ema(low, ma2_input)
ma1op = ma1 * (1 + oppercent_1 / 100)
ma2op = ma2 * (1 + oppercent_1 / 100)

// 魔鬼滚仓核心变量
var float initial_capital = strategy.initial_capital
var float total_profit = 0.0
var float base_entry_price = 0.0
var float dynamic_stop_price = 0.0
var int consecutive_wins = 0
var int consecutive_losses = 0
var bool freeze_trading = false
var int freeze_start_time = 0
var float daily_profit = 0.0
var int last_trade_day = 0
var float max_profit_today = 0.0

// 当前持仓信息
current_position_size = strategy.position_size
avg_entry_price = strategy.position_avg_price
current_profit_percent = current_position_size != 0 ? (close - avg_entry_price) / avg_entry_price * 100 : 0.0
current_profit_amount = current_position_size * (close - avg_entry_price)

// 波动率计算
atr_period = 14
current_atr = ta.atr(atr_period)
price_volatility = current_atr / close * 100

// 趋势强度判断
trend_strength = math.abs(ta.change(close, 5)) / close * 100
is_extreme_market = price_volatility > volatility_threshold or trend_strength > 10.0

// 动态止损计算
calculate_dynamic_stop() =>
    if current_position_size > 0 and avg_entry_price > 0
        base_stop = avg_entry_price * (1 - base_stop_percent / 100)

        // 根据盈利情况调整止损
        if current_profit_percent > 0
            profit_steps = math.floor(current_profit_percent / (is_extreme_market ? extreme_market_step : trailing_profit_step))
            stop_adjustment = profit_steps * (is_extreme_market ? extreme_market_ratio : trailing_stop_ratio) * (is_extreme_market ? extreme_market_step : trailing_profit_step) / 100
            adjusted_stop = avg_entry_price * (1 + stop_adjustment)
            math.max(base_stop, adjusted_stop)
        else
            base_stop
    else
        0.0

dynamic_stop_price := calculate_dynamic_stop()

// 仓位大小计算
calculate_position_size(profit_level) =>
    base_size = strategy.equity * initial_risk_percent / 100 / close

    if not enable_rolling
        base_size
    else
        switch profit_level
            1 => base_size * 1.5  // 20%盈利档位
            2 => base_size * 2.0  // 50%盈利档位
            3 => base_size * 3.0  // 100%盈利档位（雪崩模式）
            => base_size

//条件==========================================================================
// 主体策略信号（保持不变）
long = close > ma1op
long_tp = close < ma2

// 魔鬼滚仓风控条件（接入时间风控与过滤）

// 每日盈利重置
current_day_int = dayofmonth(time)
if current_day_int != last_trade_day
    daily_profit := 0.0
    max_profit_today := 0.0
    last_trade_day := current_day_int

// 连胜连败统计更新
if strategy.closedtrades > strategy.closedtrades[1]
    last_trade_profit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    if last_trade_profit > 0
        consecutive_wins := consecutive_wins + 1
        consecutive_losses := 0
    else
        consecutive_losses := consecutive_losses + 1
        consecutive_wins := 0

// 自毁机制 - 使用可调参数
if enable_freeze_mechanism and consecutive_losses >= max_consecutive_losses and not freeze_trading
    freeze_trading := true
    freeze_start_time := time

// 解除冻结检查（使用可调时长）
freeze_duration_ms = freeze_hours * 3600000  // 小时转毫秒
if freeze_trading and (time - freeze_start_time) >= freeze_duration_ms
    freeze_trading := false
    consecutive_losses := 0

// 当日盈利监控
daily_profit := strategy.equity - strategy.initial_capital
daily_profit_percent = daily_profit / strategy.initial_capital * 100
max_profit_today := math.max(max_profit_today, daily_profit_percent)

// 提现熔断机制 - 使用可调参数
should_withdraw = enable_withdrawal_trigger and daily_profit_percent > daily_profit_limit

// 加仓触发条件判断
can_add_position = strategy.opentrades < max_pyramid_levels and enable_rolling and not freeze_trading

// 盈利档位判断
profit_level_1_trigger = current_profit_percent >= profit_threshold_1 and consecutive_wins >= consecutive_wins_trigger
profit_level_2_trigger = current_profit_percent >= profit_threshold_2 and price_volatility > volatility_threshold
profit_level_3_trigger = current_profit_percent >= profit_threshold_3 and is_extreme_market

// 最终交易条件（叠加时间风控、极端波动过滤、提现熔断）
can_open_long = long and not freeze_trading and can_trade_time and not should_withdraw and (enable_extreme_filter ? not is_extreme_market : true)
can_add_long_1 = profit_level_1_trigger and can_add_position
can_add_long_2 = profit_level_2_trigger and can_add_position
can_add_long_3 = profit_level_3_trigger and can_add_position

// 止损条件
should_stop_loss = current_position_size > 0 and close <= dynamic_stop_price
should_take_profit = long_tp or should_withdraw

//显示==========================================================================
// 主体策略显示（保持不变）
stLongplot=plot(ma1, color = color.new(color.white, 0), title = 'MA1')
stShortplot=plot(ma2, color = color.new(color.red, 0), title = 'MA2')
fill(stLongplot,stShortplot,color= color.rgb(0,255,0,0.1))

// 魔鬼滚仓显示系统
plot(avg_entry_price, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '平均开仓价')
plot(dynamic_stop_price, color = color.new(color.red, 60), style = plot.style_linebr, linewidth = 2, title = '动态止损价')

// 盈利档位标记
plotshape(can_add_long_1, style=shape.triangleup, location=location.belowbar, color=color.yellow, size=size.small, title="20%盈利加仓")
plotshape(can_add_long_2, style=shape.triangleup, location=location.belowbar, color=color.orange, size=size.normal, title="50%盈利加仓")
plotshape(can_add_long_3, style=shape.triangleup, location=location.belowbar, color=color.red, size=size.large, title="雪崩模式")

// 风控状态标记
plotshape(freeze_trading, style=shape.xcross, location=location.abovebar, color=color.purple, size=size.large, title="交易冻结")
plotshape(should_withdraw, style=shape.diamond, location=location.abovebar, color=color.blue, size=size.normal, title="提现信号")

// 信息面板
var table info_table = table.new(position.top_right, 3, 8, bgcolor=color.new(color.white, 80), border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "🔥 魔鬼滚仓状态", text_color=color.red, text_size=size.normal)
    table.cell(info_table, 1, 0, "", text_color=color.black)
    table.cell(info_table, 2, 0, "", text_color=color.black)

    table.cell(info_table, 0, 1, "当前盈利", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(current_profit_percent, "#.##") + "%", text_color=current_profit_percent > 0 ? color.green : color.red)
    table.cell(info_table, 2, 1, str.tostring(current_profit_amount, "#.##") + "U", text_color=current_profit_amount > 0 ? color.green : color.red)

    table.cell(info_table, 0, 2, "连胜次数", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(consecutive_wins), text_color=consecutive_wins >= consecutive_wins_trigger ? color.green : color.gray)
    table.cell(info_table, 2, 2, "连败:" + str.tostring(consecutive_losses), text_color=consecutive_losses >= 2 ? color.red : color.gray)

    table.cell(info_table, 0, 3, "当日盈利", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(daily_profit_percent, "#.##") + "%", text_color=daily_profit_percent > 0 ? color.green : color.red)
    table.cell(info_table, 2, 3, should_withdraw ? "🚨提现" : "正常", text_color=should_withdraw ? color.red : color.green)

    table.cell(info_table, 0, 4, "波动率", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(price_volatility, "#.##") + "%", text_color=price_volatility > volatility_threshold ? color.orange : color.gray)
    table.cell(info_table, 2, 4, is_extreme_market ? "极端" : "正常", text_color=is_extreme_market ? color.red : color.green)

    table.cell(info_table, 0, 5, "持仓层数", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(strategy.opentrades), text_color=strategy.opentrades > 3 ? color.orange : color.green)
    table.cell(info_table, 2, 5, str.tostring(max_pyramid_levels), text_color=color.gray)

    table.cell(info_table, 0, 6, "交易状态", text_color=color.black)
    table.cell(info_table, 1, 6, freeze_trading ? "🔒冻结" : "🟢正常", text_color=freeze_trading ? color.red : color.green)
    table.cell(info_table, 2, 6, can_trade_time ? "允许交易" : "禁止交易", text_color=can_trade_time ? color.green : color.gray)

//回测==========================================================================
ACT_BT = input.bool(true, title = '回测开关', group = '回测')
testStartYear = input.int(2023, title = '开始年份', minval = 1990, maxval = 2222, group = '回测')
testStartMonth = input.int(01, title = '开始月份', minval = 1, maxval = 12, group = '回测')
testStartDay = input.int(01, title = '开始日期', minval = 1, maxval = 31, group = '回测')
testPeriodStart = timestamp(testStartYear, testStartMonth, testStartDay, 0, 0)
testStopYear = input.int(2030, title = '终止年份', minval = 1980, maxval = 2222, group = '回测')
testStopMonth = input.int(12, title = '终止月份', minval = 1, maxval = 12, group = '回测')
testStopDay = input.int(31, title = '终止日期', minval = 1, maxval = 31, group = '回测')
testPeriodStop = timestamp(testStopYear, testStopMonth, testStopDay, 0, 0)
testPeriod = time >= testPeriodStart and time <= testPeriodStop ? true : false

//警报==========================================================================
alert_open = '🔥魔鬼滚仓开仓 bot '
alert_add_1 = '⚡20%盈利加仓 bot '
alert_add_2 = '🚀50%盈利加仓 bot '
alert_add_3 = '💥雪崩模式加仓 bot '
alert_close = '✅平仓 bot '
alert_stop = '❌止损 bot '
alert_withdraw = '💰提现信号 bot '

//策略==========================================================================
// 魔鬼滚仓交易执行系统

if ACT_BT and testPeriod

    // 首单开仓 - 5-10%资金，3%止损
    if can_open_long and strategy.position_size == 0
        base_position_size = calculate_position_size(0)
        strategy.entry('首单', strategy.long, qty=base_position_size, comment='🔥首单开仓', alert_message=alert_open)
        base_entry_price := close

    // 20%盈利档位加仓 - 利润×1.5
    if can_add_long_1
        add_size_1 = calculate_position_size(1)
        strategy.entry('加仓1', strategy.long, qty=add_size_1, comment='⚡20%加仓', alert_message=alert_add_1)

    // 50%盈利档位加仓 - 利润×2
    if can_add_long_2
        add_size_2 = calculate_position_size(2)
        strategy.entry('加仓2', strategy.long, qty=add_size_2, comment='🚀50%加仓', alert_message=alert_add_2)

    // 100%盈利雪崩模式 - 利润全押
    if can_add_long_3
        add_size_3 = calculate_position_size(3)
        strategy.entry('雪崩', strategy.long, qty=add_size_3, comment='💥雪崩模式', alert_message=alert_add_3)

    // 动态止损系统
    if should_stop_loss
        strategy.close_all(comment='❌动态止损', alert_message=alert_stop)

    // 止盈系统
    if should_take_profit
        if should_withdraw
            strategy.close_all(comment='💰提现止盈', alert_message=alert_withdraw)
        else
            strategy.close_all(comment='✅正常止盈', alert_message=alert_close)

// 风险警告系统
if barstate.islast
    // 连败警告 - 使用可调参数
    if consecutive_losses >= (max_consecutive_losses - 1)
        runtime.error("⚠️ 连败" + str.tostring(consecutive_losses) + "次，注意风险控制！")

    // 当日盈利过高警告 - 使用可调参数
    if daily_profit_percent > (daily_profit_limit * 0.8)
        runtime.error("🚨 当日盈利" + str.tostring(daily_profit_percent, "#.##") + "%，建议提现！")

    // 极端市场警告
    if is_extreme_market and strategy.position_size > 0
        runtime.error("⚡ 检测到极端市场，动态止损已激活！")
